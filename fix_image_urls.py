#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复图片URL问题的脚本
专门处理图片链接过滤问题
"""

import json
import os
import re

def is_valid_question_image(url):
    """
    判断是否是有效的题目图片URL
    """
    if not url:
        return False
    
    # 正确的题目图片特征
    valid_patterns = [
        r'up\.zaixiankaoshi\.com',  # 你提到的正确域名
        r'question/',
        r'upload/',
        r'exam/',
        r'test/',
        r'docs/',
        r'content/',
        r'media/'
    ]
    
    # 排除的UI元素
    invalid_patterns = [
        r'icon-cut_topic\.png',  # 特定的UI图标
        r'logo',
        r'icon',
        r'avatar',
        r'header',
        r'footer',
        r'nav',
        r'menu',
        r'button',
        r'bg',
        r'background',
        r'arrow',
        r'beian',
        r'def-head',
        r'_nuxt',
        r'practice/icon',
        r'ui/',
        r'static/',
        r'assets/'
    ]
    
    # 检查是否匹配有效模式
    has_valid_pattern = any(re.search(pattern, url, re.IGNORECASE) for pattern in valid_patterns)
    
    # 检查是否包含无效模式
    has_invalid_pattern = any(re.search(pattern, url, re.IGNORECASE) for pattern in invalid_patterns)
    
    return has_valid_pattern and not has_invalid_pattern

def fix_image_urls_in_json(json_file):
    """
    修复JSON文件中的图片URL
    """
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        fixed_count = 0
        total_questions = len(data)
        
        for question in data:
            if 'images' in question and question['images']:
                original_images = question['images'][:]
                valid_images = []
                
                for img_url in original_images:
                    if is_valid_question_image(img_url):
                        valid_images.append(img_url)
                    else:
                        print(f"❌ 排除无效图片: {img_url[:80]}...")
                
                if len(valid_images) != len(original_images):
                    question['images'] = valid_images
                    fixed_count += 1
                    print(f"✅ 修复题目图片: {len(original_images)} -> {len(valid_images)}")
        
        # 保存修复后的文件
        output_file = json_file.replace('.json', '_fixed_images.json')
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"\n修复完成:")
        print(f"  总题目数: {total_questions}")
        print(f"  修复题目数: {fixed_count}")
        print(f"  输出文件: {output_file}")
        
        return output_file
        
    except Exception as e:
        print(f"修复JSON文件出错: {e}")
        return None

def analyze_image_urls(json_file):
    """
    分析JSON文件中的图片URL模式
    """
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        all_urls = []
        for question in data:
            if 'images' in question and question['images']:
                all_urls.extend(question['images'])
        
        print(f"\n=== 图片URL分析 ===")
        print(f"总图片数: {len(all_urls)}")
        
        # 按域名分组
        domain_count = {}
        for url in all_urls:
            try:
                domain = re.search(r'https?://([^/]+)', url).group(1)
                domain_count[domain] = domain_count.get(domain, 0) + 1
            except:
                domain_count['unknown'] = domain_count.get('unknown', 0) + 1
        
        print("\n域名分布:")
        for domain, count in sorted(domain_count.items(), key=lambda x: x[1], reverse=True):
            print(f"  {domain}: {count} 张")
        
        # 分析URL模式
        print("\n所有图片URL:")
        for i, url in enumerate(all_urls, 1):
            valid = "✅" if is_valid_question_image(url) else "❌"
            print(f"  {i}. {valid} {url}")
            
    except Exception as e:
        print(f"分析图片URL出错: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 图片URL修复工具")
    print("=" * 60)
    
    # 查找最新的JSON文件
    scraped_dir = "scraped_questions"
    if not os.path.exists(scraped_dir):
        print("❌ 未找到scraped_questions目录")
        return
    
    json_files = [f for f in os.listdir(scraped_dir) if f.endswith('_raw_data.json')]
    if not json_files:
        print("❌ 未找到原始数据JSON文件")
        return
    
    # 使用最新的文件
    latest_file = max(json_files, key=lambda f: os.path.getctime(os.path.join(scraped_dir, f)))
    json_path = os.path.join(scraped_dir, latest_file)
    
    print(f"📁 处理文件: {latest_file}")
    
    # 分析当前图片URL
    print("\n🔍 分析当前图片URL...")
    analyze_image_urls(json_path)
    
    # 修复图片URL
    print("\n🔧 开始修复图片URL...")
    fixed_file = fix_image_urls_in_json(json_path)
    
    if fixed_file:
        print(f"\n✅ 修复完成！")
        print(f"原文件: {json_path}")
        print(f"修复后: {fixed_file}")
        
        # 分析修复后的结果
        print("\n🔍 分析修复后的图片URL...")
        analyze_image_urls(fixed_file)
    
    print("\n🎉 处理完成！")

if __name__ == "__main__":
    main()
