#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考试宝题库Selenium自动化脚本
使用Chrome浏览器自动访问考试宝题库页面
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import time
import json
import os
import re
import requests
import base64
from datetime import datetime

def download_image(image_url, save_dir, question_number, image_index):
    """
    下载图片到指定目录
    """
    try:
        if image_url.startswith('data:'):
            # 处理base64编码的图片
            header, data = image_url.split(',', 1)
            image_data = base64.b64decode(data)

            # 从header中获取文件扩展名
            if 'jpeg' in header or 'jpg' in header:
                ext = 'jpg'
            elif 'png' in header:
                ext = 'png'
            elif 'gif' in header:
                ext = 'gif'
            else:
                ext = 'jpg'  # 默认

            filename = f"question_{question_number}_image_{image_index}.{ext}"
            filepath = os.path.join(save_dir, filename)

            with open(filepath, 'wb') as f:
                f.write(image_data)

            print(f"✅ 下载base64图片: {filename}")
            return filename

        else:
            # 处理HTTP URL图片
            response = requests.get(image_url, timeout=10)
            response.raise_for_status()

            # 从URL或Content-Type获取文件扩展名
            content_type = response.headers.get('content-type', '')
            if 'jpeg' in content_type or 'jpg' in content_type:
                ext = 'jpg'
            elif 'png' in content_type:
                ext = 'png'
            elif 'gif' in content_type:
                ext = 'gif'
            else:
                # 从URL获取扩展名
                ext = image_url.split('.')[-1].split('?')[0]
                if ext not in ['jpg', 'jpeg', 'png', 'gif']:
                    ext = 'jpg'

            filename = f"question_{question_number}_image_{image_index}.{ext}"
            filepath = os.path.join(save_dir, filename)

            with open(filepath, 'wb') as f:
                f.write(response.content)

            print(f"✅ 下载网络图片: {filename}")
            return filename

    except Exception as e:
        print(f"❌ 下载图片失败 {image_url}: {e}")
        return None

def _is_likely_content_image(img_element):
    """
    判断图片是否可能是内容图片（而不是UI图标）
    """
    try:
        # 获取图片尺寸
        width = img_element.get_attribute('width') or img_element.size.get('width', 0)
        height = img_element.get_attribute('height') or img_element.size.get('height', 0)

        # 转换为数字
        try:
            width = int(float(width)) if width else 0
            height = int(float(height)) if height else 0
        except:
            width = height = 0

        # 检查图片URL
        src = img_element.get_attribute('src') or ''

        # 如果是特定的UI图标，直接排除
        if 'icon-cut_topic.png' in src:
            return False

        # 如果是题目图片域名，优先考虑
        if 'up.zaixiankaoshi.com' in src:
            return True

        # 如果图片太小，可能是图标
        if width > 0 and height > 0:
            if width < 30 or height < 30:
                return False
            # 如果图片太小且是正方形，可能是图标
            if width < 80 and height < 80 and abs(width - height) < 10:
                return False

        # 检查alt属性
        alt = img_element.get_attribute('alt') or ''
        if any(keyword in alt.lower() for keyword in ['icon', 'logo', 'avatar', 'button', 'cut']):
            return False

        # 检查class属性
        class_name = img_element.get_attribute('class') or ''
        if any(keyword in class_name.lower() for keyword in ['icon', 'logo', 'ui', 'button']):
            return False

        return True
    except:
        return False  # 如果无法判断，默认不认为是内容图片

def init_browser():
    """
    初始化浏览器（兼容函数）
    """
    return create_chrome_driver()

def create_chrome_driver():
    """
    创建Chrome驱动
    """
    print("正在启动Chrome浏览器...")
    
    # Chrome选项配置
    chrome_options = Options()
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36')
    chrome_options.add_argument('--window-size=1920,1080')
    chrome_options.add_argument('--lang=zh-CN')
    chrome_options.add_argument('--disable-notifications')
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    try:
        # 使用当前目录的chromedriver.exe
        service = Service('./chromedriver.exe')
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # 隐藏webdriver属性
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("✅ Chrome浏览器启动成功！")
        return driver
        
    except Exception as e:
        print(f"❌ Chrome浏览器启动失败: {e}")
        return None

def set_cookies(driver):
    """
    设置cookies
    """
    print("正在设置cookies...")
    
    # 先访问主域名
    driver.get("https://www.kaoshibao.com")
    time.sleep(2)
    
    # cookies数据
    cookies_data = {
        'UM_distinctid': '1982bde8f2d26-0934442fa5c2d98-********-1fa400-1982bde8f2e720',
        'uu': '4585e108-8fab-4598-8a0d-a6dc8d93631d',
        'Hm_lvt_975400bd703f587eef8de1efe396089d': '**********,**********',
        'HMACCOUNT': 'F926D33331A243EC',
        'source': '',
        'invitation': '',
        'token': 'K042Yf/XgtjTwNzrQlywckfAq93e3dPsQyWxAtkRNbc=',
        'SOTOKEN': 'K042Yf/XgtjTwNzrQlywckfAq93e3dPsQyWxAtkRNbc=',
        'Hm_lpvt_975400bd703f587eef8de1efe396089d': '**********',
        'CNZZDATA1278923901': '**********-**********-https%253A%252F%252Fwww.google.com%252F%7C**********'
    }
    
    # 添加cookies
    success_count = 0
    for name, value in cookies_data.items():
        try:
            driver.add_cookie({
                'name': name,
                'value': value,
                'domain': '.kaoshibao.com',
                'path': '/'
            })
            success_count += 1
        except Exception as e:
            print(f"添加cookie失败 {name}: {e}")
    
    print(f"✅ 成功添加 {success_count} 个cookies")

def visit_page(driver):
    """
    访问考试宝题库页面
    """
    # 目标URL - 带各种参数
    url = "https://www.kaoshibao.com/online/?paperId=12154502&practice=&modal=1&is_recite=&qtype=&text=%E9%A1%BA%E5%BA%8F%E7%BB%83%E4%B9%A0&sequence=0&is_collect=1&is_vip_paper=0&gid="
    
    print("正在访问考试宝题库页面...")
    print(f"URL: {url}")
    
    try:
        # 访问页面
        driver.get(url)
        time.sleep(5)  # 等待页面加载
        
        # 获取页面信息
        title = driver.title
        current_url = driver.current_url
        page_source_length = len(driver.page_source)
        
        print(f"✅ 页面访问成功！")
        print(f"页面标题: {title}")
        print(f"当前URL: {current_url}")
        print(f"页面源码长度: {page_source_length} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 访问页面失败: {e}")
        return False

def enable_recite_mode(driver):
    """
    自动开启背题模式
    """
    print("\n正在查找并开启背题模式...")

    try:
        # 等待页面完全加载
        WebDriverWait(driver, 10).until(
            lambda d: d.execute_script("return document.readyState") == "complete"
        )
        time.sleep(2)

        # 多种可能的背题模式开关选择器
        recite_selectors = [
            # 根据文本内容查找
            "//span[contains(text(), '背题模式')]/..//input[@type='checkbox']",
            "//span[contains(text(), '背题模式')]/..//div[contains(@class, 'switch')]",
            "//label[contains(text(), '背题模式')]//input",
            "//div[contains(text(), '背题模式')]/..//input",
            # 根据class查找开关
            ".switch input[type='checkbox']",
            ".toggle input[type='checkbox']",
            "input[type='checkbox'][class*='switch']",
            # 通用开关选择器
            ".el-switch__input",
            ".ant-switch",
            ".switch-input"
        ]

        recite_found = False

        for selector in recite_selectors:
            try:
                if selector.startswith("//"):
                    # XPath选择器
                    elements = driver.find_elements(By.XPATH, selector)
                else:
                    # CSS选择器
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)

                if elements:
                    for element in elements:
                        try:
                            # 检查元素是否可见和可点击
                            if element.is_displayed() and element.is_enabled():
                                # 检查是否已经开启
                                is_checked = element.is_selected() or element.get_attribute("checked")

                                if not is_checked:
                                    # 尝试点击开关
                                    driver.execute_script("arguments[0].click();", element)
                                    time.sleep(1)
                                    print("✅ 成功点击背题模式开关")
                                    recite_found = True
                                    break
                                else:
                                    print("✅ 背题模式已经开启")
                                    recite_found = True
                                    break
                        except Exception as e:
                            continue

                if recite_found:
                    break

            except Exception as e:
                continue

        # 如果没找到checkbox，尝试查找可点击的开关元素
        if not recite_found:
            print("未找到checkbox，尝试查找可点击的开关元素...")

            clickable_selectors = [
                "//span[contains(text(), '背题模式')]/..",
                "//div[contains(text(), '背题模式')]",
                "//label[contains(text(), '背题模式')]",
                ".switch",
                ".toggle",
                "[class*='switch']"
            ]

            for selector in clickable_selectors:
                try:
                    if selector.startswith("//"):
                        elements = driver.find_elements(By.XPATH, selector)
                    else:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)

                    if elements:
                        for element in elements:
                            try:
                                if element.is_displayed() and element.is_enabled():
                                    # 检查元素文本是否包含"背题"相关内容
                                    element_text = element.text.lower()
                                    if "背题" in element_text or "recite" in element_text:
                                        driver.execute_script("arguments[0].click();", element)
                                        time.sleep(1)
                                        print("✅ 成功点击背题模式开关")
                                        recite_found = True
                                        break
                            except Exception as e:
                                continue

                    if recite_found:
                        break

                except Exception as e:
                    continue

        if not recite_found:
            print("⚠️  未找到背题模式开关，可能需要手动开启")

        return recite_found

    except Exception as e:
        print(f"❌ 开启背题模式时出错: {e}")
        return False

def extract_question_data(driver):
    """
    提取当前页面的题目数据
    """
    try:
        # 等待页面加载完成
        time.sleep(2)

        question_data = {
            'question': '',
            'options': [],
            'answer': '',
            'explanation': '',
            'question_type': '',
            'images': []  # 添加图片字段
        }

        # 提取题目内容 - 基于页面源码分析的精确选择器
        question_selectors = [
            # 基于页面源码的精确选择器
            ".qusetion-title",  # 注意：页面中是 qusetion-title (拼写错误)
            ".topic-tit .qusetion-title",
            ".topic .qusetion-title",
            # 备用选择器
            ".question-content",
            ".topic-content",
            ".item-content",
            ".question",
            ".topic",
            "[class*='question']",
            "[class*='topic']"
        ]

        question_found = False
        for selector in question_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    for element in elements:
                        text = element.text.strip()
                        if text and len(text) > 5:  # 过滤掉太短的文本
                            # 清理题目文本
                            clean_text = text.replace('\n', ' ').replace('\r', ' ')
                            clean_text = ' '.join(clean_text.split())  # 合并多个空格
                            question_data['question'] = clean_text
                            question_found = True
                            print(f"✅ 找到题目内容: {clean_text[:100]}...")
                            break
                if question_found:
                    break
            except Exception as e:
                print(f"题目选择器 {selector} 失败: {e}")
                continue

        # 如果没找到，尝试从页面源码中提取
        if not question_found:
            page_source = driver.page_source
            # 使用正则表达式查找题目模式
            question_patterns = [
                r'<div[^>]*class="[^"]*question[^"]*"[^>]*>(.*?)</div>',
                r'<span[^>]*class="[^"]*topic[^"]*"[^>]*>(.*?)</span>',
                r'题目[：:]\s*([^<]+)',
                r'问题[：:]\s*([^<]+)'
            ]

            for pattern in question_patterns:
                matches = re.findall(pattern, page_source, re.DOTALL | re.IGNORECASE)
                if matches:
                    # 清理HTML标签
                    question_text = re.sub(r'<[^>]+>', '', matches[0]).strip()
                    if len(question_text) > 10:
                        question_data['question'] = question_text
                        question_found = True
                        break

        # 提取选项 - 基于页面源码分析
        option_selectors = [
            # 基于页面源码的精确选择器
            ".options-w .option",  # 选项容器中的选项
            ".select-left .option",  # 左侧选择区域的选项
            ".option",  # 通用选项选择器
            # 备用选择器
            ".choice",
            ".answer-option",
            "[class*='option']",
            "[class*='choice']"
        ]

        options_found = False
        for selector in option_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    options = []
                    for element in elements:
                        text = element.text.strip()
                        if text and len(text) > 0:
                            # 清理选项文本
                            clean_text = text.replace('\n', ' ').replace('\r', ' ')
                            clean_text = ' '.join(clean_text.split())

                            # 修复重复选项标签问题 (如 "A. A 内容" -> "A. 内容")
                            clean_text = re.sub(r'^([A-Z])[.、]\s*\1\s+', r'\1. ', clean_text)
                            # 修复其他重复模式 (如 "A. B 内容" -> "A. 内容")
                            clean_text = re.sub(r'^([A-Z])[.、]\s*[A-Z]\s+', r'\1. ', clean_text)

                            options.append(clean_text)

                    if options:
                        question_data['options'] = options
                        options_found = True
                        print(f"✅ 找到 {len(options)} 个选项")
                        for i, opt in enumerate(options):
                            print(f"   选项{i+1}: {opt[:50]}...")
                        break
            except Exception as e:
                print(f"选项选择器 {selector} 失败: {e}")
                continue

        # 如果没找到选项，尝试从页面源码提取
        if not options_found:
            page_source = driver.page_source
            # 查找A、B、C、D选项模式
            option_patterns = [
                r'[ABCD][.、]\s*([^<\n]+)',
                r'选项[ABCD][：:]\s*([^<\n]+)'
            ]

            for pattern in option_patterns:
                matches = re.findall(pattern, page_source, re.IGNORECASE)
                if matches:
                    # 清理提取的选项，添加正确的标签
                    clean_options = []
                    for i, match in enumerate(matches):
                        clean_match = match.strip()
                        option_label = chr(65 + i)  # A, B, C, D...
                        clean_options.append(f"{option_label}. {clean_match}")

                    question_data['options'] = clean_options
                    options_found = True
                    break

        # 提取图片 - 只提取真正的题目图片
        print("🖼️ 检查是否有题目相关图片...")
        images = []
        try:
            # 查找所有图片元素
            all_img_elements = driver.find_elements(By.TAG_NAME, "img")

            for img in all_img_elements:
                if img.is_displayed():
                    src = img.get_attribute('src')
                    if src and src.startswith('http'):
                        # 只保留真正的题目图片，排除UI图标
                        if (
                            # 包含题目图片的特征域名或路径
                            any(domain in src for domain in [
                                'up.zaixiankaoshi.com',  # 你提到的正确域名
                                'question/',
                                'upload/',
                                'exam/',
                                'test/',
                                'docs/'
                            ]) and
                            # 排除明显的UI元素
                            not any(exclude in src.lower() for exclude in [
                                'icon-cut_topic.png',  # 排除这个特定的UI图标
                                'logo', 'icon', 'avatar', 'header', 'footer',
                                'nav', 'menu', 'button', 'bg', 'background',
                                'arrow', 'beian', 'def-head', '_nuxt',
                                'practice/icon',  # 排除练习相关的图标
                                'ui/', 'static/', 'assets/'
                            ]) and
                            # 图片尺寸检查
                            _is_likely_content_image(img)
                        ):
                            images.append(src)
                            print(f"✅ 找到题目图片: {src[:100]}...")

            # 如果没找到图片，尝试查找base64编码的图片
            if not images:
                for img in all_img_elements:
                    if img.is_displayed():
                        src = img.get_attribute('src')
                        if src and src.startswith('data:image/'):
                            # 检查图片尺寸，确保不是小图标
                            if _is_likely_content_image(img):
                                images.append(src)
                                print(f"✅ 找到base64题目图片: data:image/...")

            # 如果还是没找到，检查是否有隐藏的图片或特殊属性
            if not images:
                # 查找可能包含图片URL的其他属性
                for img in all_img_elements:
                    if img.is_displayed():
                        # 检查data-src等属性
                        data_src = img.get_attribute('data-src')
                        if data_src and 'up.zaixiankaoshi.com' in data_src:
                            images.append(data_src)
                            print(f"✅ 找到data-src图片: {data_src[:100]}...")

                        # 检查其他可能的图片属性
                        for attr in ['data-original', 'data-lazy', 'data-url']:
                            attr_value = img.get_attribute(attr)
                            if attr_value and 'up.zaixiankaoshi.com' in attr_value:
                                images.append(attr_value)
                                print(f"✅ 找到{attr}图片: {attr_value[:100]}...")

            # 去重
            images = list(set(images))

            if images:
                question_data['images'] = images
                print(f"✅ 总共找到 {len(images)} 张题目相关图片")
            else:
                print("ℹ️  当前题目无图片")

        except Exception as e:
            print(f"提取图片时出错: {e}")

        # 提取答案 - 全面搜索页面中的答案信息
        print("🔍 开始搜索正确答案...")

        # 首先尝试从所有可见文本中搜索答案
        try:
            # 获取页面所有文本内容
            body_text = driver.find_element(By.TAG_NAME, "body").text
            print(f"📄 页面文本长度: {len(body_text)} 字符")

            # 多种答案格式匹配
            answer_patterns = [
                r'正确答案[：:]\s*([A-Z])',      # 正确答案：C
                r'答案[：:]\s*([A-Z])',         # 答案：C
                r'正确答案\s*([A-Z])',          # 正确答案 C
                r'答案\s*([A-Z])',              # 答案 C
                r'([A-Z])\s*正确',              # C 正确
                r'选择\s*([A-Z])',              # 选择 C
                r'正确选项[：:]\s*([A-Z])',     # 正确选项：C
                r'标准答案[：:]\s*([A-Z])',     # 标准答案：C
            ]

            for pattern in answer_patterns:
                matches = re.findall(pattern, body_text, re.IGNORECASE)
                if matches:
                    question_data['answer'] = matches[-1].upper()  # 取最后一个匹配，通常是最准确的
                    print(f"✅ 从页面文本找到答案: {question_data['answer']} (模式: {pattern})")
                    answer_found = True
                    break

        except Exception as e:
            print(f"从页面文本提取答案失败: {e}")

        # 如果还没找到，尝试特定的DOM元素
        if not answer_found:
            answer_selectors = [
                # 可能包含答案的各种选择器
                "*[class*='answer']",
                "*[class*='correct']",
                "*[class*='result']",
                "*[class*='statistics']",
                "*[class*='info']",
                "*[id*='answer']",
                "*[id*='correct']",
                # 通用选择器
                "div", "span", "p", "li", "td"
            ]

            for selector in answer_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            text = element.text.strip()
                            if text and len(text) < 100:  # 答案通常很短
                                # 检查是否包含答案信息
                                for pattern in answer_patterns:
                                    match = re.search(pattern, text, re.IGNORECASE)
                                    if match:
                                        question_data['answer'] = match.group(1).upper()
                                        print(f"✅ 从DOM元素找到答案: {question_data['answer']} (选择器: {selector})")
                                        answer_found = True
                                        break
                                if answer_found:
                                    break
                    if answer_found:
                        break
                except Exception as e:
                    continue

        # 最后尝试从页面源码搜索
        if not answer_found:
            try:
                print("🔍 从页面源码搜索答案...")
                page_source = driver.page_source

                # 扩展的源码搜索模式
                source_patterns = [
                    r'正确答案[：:]\s*([A-Z])',
                    r'"correctAnswer"\s*:\s*"([A-Z])"',
                    r'"answer"\s*:\s*"([A-Z])"',
                    r'correctAnswer["\']?\s*:\s*["\']?([A-Z])["\']?',
                    r'data-answer\s*=\s*["\']([A-Z])["\']',
                    r'answer["\']?\s*:\s*["\']?([A-Z])["\']?',
                    r'正确选项[：:]\s*([A-Z])',
                    r'标准答案[：:]\s*([A-Z])',
                    # JSON格式
                    r'"correct"\s*:\s*"([A-Z])"',
                    r'"right"\s*:\s*"([A-Z])"',
                ]

                for pattern in source_patterns:
                    matches = re.findall(pattern, page_source, re.IGNORECASE)
                    if matches:
                        question_data['answer'] = matches[-1].upper()
                        print(f"✅ 从页面源码找到答案: {question_data['answer']} (模式: {pattern})")
                        answer_found = True
                        break

            except Exception as e:
                print(f"从页面源码提取答案失败: {e}")

        if not answer_found:
            print("⚠️  未找到正确答案")
            # 输出页面部分文本用于调试
            try:
                body_text = driver.find_element(By.TAG_NAME, "body").text
                # 查找可能包含答案的文本片段
                lines = body_text.split('\n')
                for line in lines:
                    if any(keyword in line for keyword in ['答案', '正确', 'answer', 'correct']):
                        print(f"🔍 可能包含答案的文本: {line.strip()}")
            except:
                pass

        # 提取解析
        explanation_selectors = [
            ".explanation",
            ".analysis",
            ".解析",
            "[class*='explanation']",
            "[class*='analysis']"
        ]

        explanation_found = False
        for selector in explanation_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    for element in elements:
                        text = element.text.strip()
                        if text and ('解析' in text or 'explanation' in text.lower()):
                            # 提取解析部分
                            explanation_match = re.search(r'解析[：:]\s*([^<]+)', text)
                            if explanation_match:
                                question_data['explanation'] = explanation_match.group(1).strip()
                                explanation_found = True
                                break
                if explanation_found:
                    break
            except:
                continue

        # 判断题目类型
        if question_data['options']:
            if len(question_data['options']) <= 2:
                question_data['question_type'] = '判断题'
            elif any('填空' in opt or '____' in opt for opt in question_data['options']):
                question_data['question_type'] = '填空题'
            else:
                question_data['question_type'] = '选择题'
        elif '填空' in question_data['question'] or '____' in question_data['question']:
            question_data['question_type'] = '填空题'
        elif any(word in question_data['question'] for word in ['正确', '错误', '对', '错', '是否']):
            question_data['question_type'] = '判断题'
        else:
            question_data['question_type'] = '简答题'

        return question_data

    except Exception as e:
        print(f"❌ 提取题目数据时出错: {e}")
        return None

def format_question_data(question_data, question_number, downloaded_images=None):
    """
    格式化题目数据为指定格式
    按照用户要求的格式：题目序号+题目内容，然后每个选项单独一行，最后是答案
    支持图片引用
    """
    if not question_data or not question_data['question']:
        return ""

    formatted_text = ""

    # 1. 题目序号和内容
    formatted_text += f"{question_number}.{question_data['question']}"

    # 2. 图片引用（如果有）
    if downloaded_images:
        for i, img_filename in enumerate(downloaded_images):
            formatted_text += f" [图片{i+1}: {img_filename}]"

    formatted_text += "\n"

    # 3. 选项（如果有）
    if question_data['options']:
        for option in question_data['options']:
            # 清理选项文本，移除重复的标签
            clean_option = option.strip()

            # 提取选项标签
            label_match = re.match(r'^([A-Z])[.、]\s*', clean_option)
            if label_match:
                label = label_match.group(1)
                # 移除标签后的内容
                content = re.sub(r'^[A-Z][.、]\s*', '', clean_option)

                # 移除重复的选项标签 (如 "A 内容" -> "内容")
                content = re.sub(r'^[A-Z]\s+', '', content)

                formatted_text += f"{label}. {content}\n"
            else:
                # 如果没有标签，直接输出
                formatted_text += f"{clean_option}\n"

    # 4. 答案（如果有）
    if question_data['answer']:
        answer = question_data['answer'].strip()
        formatted_text += f"答案：{answer}\n"

    return formatted_text

def save_questions_to_file(questions_data, filename="final_questions.txt"):
    """
    保存题目数据到文件，支持图片引用
    """
    try:
        # 创建保存目录
        save_dir = "scraped_questions"
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

        # 生成文件名（包含时间戳）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filepath = os.path.join(save_dir, f"{timestamp}_{filename}")

        # 保存格式化的题目数据
        with open(filepath, 'w', encoding='utf-8') as f:
            for i, question_data in enumerate(questions_data, 1):
                # 获取下载的图片文件名
                downloaded_images = question_data.get('downloaded_images', [])
                formatted_question = format_question_data(question_data, i, downloaded_images)
                f.write(formatted_question)

        # 同时保存JSON格式的原始数据
        json_filepath = os.path.join(save_dir, f"{timestamp}_raw_data.json")
        with open(json_filepath, 'w', encoding='utf-8') as f:
            json.dump(questions_data, f, ensure_ascii=False, indent=2)

        print(f"✅ 题目数据已保存到: {filepath}")
        print(f"✅ 原始数据已保存到: {json_filepath}")
        return filepath

    except Exception as e:
        print(f"❌ 保存题目数据时出错: {e}")
        return None

def click_next_question(driver):
    """
    点击下一题按钮 - 简化版本，专注于实际可行的方法
    """
    try:
        print("🔍 正在查找下一题按钮...")

        # 等待页面稳定
        time.sleep(2)

        # 方法1: 直接通过.next-preve容器找到第二个按钮（下一题按钮）
        try:
            next_preve_container = driver.find_element(By.CSS_SELECTOR, ".next-preve")
            buttons = next_preve_container.find_elements(By.TAG_NAME, "button")

            if len(buttons) >= 2:
                next_button = buttons[1]  # 第二个按钮是下一题

                # 滚动到按钮可见区域
                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", next_button)
                time.sleep(1)

                # 使用JavaScript强制点击，避免被其他元素遮挡
                driver.execute_script("arguments[0].click();", next_button)
                print("✅ 已点击下一题按钮 (容器方法)")
                time.sleep(3)
                return True
        except Exception as e:
            print(f"容器方法失败: {e}")

        # 方法2: 通过XPath精确定位下一题按钮
        xpath_selectors = [
            "//div[@class='next-preve']//button[2]",  # 下一题按钮是第二个
            "//button[contains(.,'下一题')]",
            "//button//div[text()='下一题']/ancestor::button",
        ]

        for xpath in xpath_selectors:
            try:
                element = driver.find_element(By.XPATH, xpath)
                if element.is_displayed() and element.is_enabled():
                    # 滚动到元素可见
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                    time.sleep(1)

                    # 使用JavaScript点击
                    driver.execute_script("arguments[0].click();", element)
                    print(f"✅ 已点击下一题按钮 (XPath: {xpath})")
                    time.sleep(3)
                    return True
            except Exception as e:
                print(f"XPath {xpath} 失败: {e}")
                continue

        # 方法3: 遍历所有按钮，找到包含"下一题"的按钮
        try:
            all_buttons = driver.find_elements(By.TAG_NAME, "button")
            print(f"🔍 找到 {len(all_buttons)} 个按钮")

            for i, button in enumerate(all_buttons):
                try:
                    if button.is_displayed() and button.is_enabled():
                        button_text = button.text.strip()
                        button_html = button.get_attribute("innerHTML")

                        # 检查是否包含"下一题"
                        if "下一题" in button_text or "下一题" in button_html:
                            print(f"🎯 找到下一题按钮 (索引 {i})")

                            # 滚动到按钮可见
                            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", button)
                            time.sleep(1)

                            # 使用JavaScript强制点击
                            driver.execute_script("arguments[0].click();", button)
                            print("✅ 已点击下一题按钮 (遍历方法)")
                            time.sleep(3)
                            return True
                except Exception as e:
                    continue
        except Exception as e:
            print(f"遍历按钮失败: {e}")

        # 方法4: 尝试模拟键盘操作
        try:
            print("🎹 尝试键盘快捷键...")

            # 先点击页面确保焦点在正确位置
            body = driver.find_element(By.TAG_NAME, "body")
            body.click()
            time.sleep(0.5)

            # 尝试右箭头键
            body.send_keys(Keys.ARROW_RIGHT)
            time.sleep(2)
            print("✅ 已使用右箭头键切换")
            return True

        except Exception as e:
            print(f"键盘操作失败: {e}")

        # 方法5: 最后尝试 - 直接执行JavaScript来触发下一题
        try:
            print("🔧 尝试JavaScript方法...")

            # 尝试执行可能的JavaScript函数
            js_commands = [
                "document.querySelector('.next-preve button:nth-child(2)').click();",
                "document.querySelector('button:contains(\"下一题\")').click();",
                "window.nextQuestion && window.nextQuestion();",
                "window.next && window.next();",
            ]

            for js_cmd in js_commands:
                try:
                    driver.execute_script(js_cmd)
                    time.sleep(2)
                    print(f"✅ JavaScript方法成功: {js_cmd}")
                    return True
                except:
                    continue

        except Exception as e:
            print(f"JavaScript方法失败: {e}")

        print("⚠️  所有方法都失败了，请手动点击下一题")
        return False

    except Exception as e:
        print(f"❌ 点击下一题按钮时出错: {e}")
        return False

def analyze_page(driver):
    """
    分析页面内容
    """
    print("\n正在分析页面内容...")

    try:
        # 检查是否需要登录
        page_text = driver.page_source.lower()
        if any(word in page_text for word in ["登录", "login", "请先登录"]):
            print("⚠️  检测到可能需要登录")
        else:
            print("✅ 未检测到登录要求")

        # 查找页面元素
        element_types = [
            (".question", "题目"),
            (".topic", "主题"),
            (".item", "项目"),
            (".option", "选项"),
            (".choice", "选择"),
            (".answer", "答案"),
            ("button", "按钮"),
            ("form", "表单"),
            ("input", "输入框")
        ]

        total_elements = 0
        for selector, name in element_types:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    count = len(elements)
                    total_elements += count
                    print(f"找到 {count} 个{name}元素")
            except:
                continue

        if total_elements > 0:
            print(f"✅ 总共找到 {total_elements} 个页面元素")
        else:
            print("⚠️  未找到明显的题目相关元素")

        # 检查页面加载状态
        ready_state = driver.execute_script("return document.readyState")
        print(f"页面加载状态: {ready_state}")

    except Exception as e:
        print(f"❌ 分析页面时出错: {e}")

def save_results(driver):
    """
    保存页面信息和截图
    """
    try:
        # 保存页面信息
        page_info = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "title": driver.title,
            "url": driver.current_url,
            "page_source_length": len(driver.page_source),
            "cookies": driver.get_cookies(),
            "user_agent": driver.execute_script("return navigator.userAgent;")
        }
        
        with open("page_info.json", 'w', encoding='utf-8') as f:
            json.dump(page_info, f, ensure_ascii=False, indent=2)
        print("✅ 页面信息已保存到 page_info.json")
        
        # 保存页面截图
        driver.save_screenshot("screenshot.png")
        print("✅ 页面截图已保存到 screenshot.png")
        
        # 保存页面源码
        with open("page_source.html", 'w', encoding='utf-8') as f:
            f.write(driver.page_source)
        print("✅ 页面源码已保存到 page_source.html")
        
    except Exception as e:
        print(f"❌ 保存结果时出错: {e}")

def interactive_mode(driver):
    """
    交互模式 - 保持浏览器打开供用户操作
    """
    print("\n" + "=" * 60)
    print("🎉 考试宝页面已在Chrome浏览器中打开！")
    print("=" * 60)
    print("您现在可以:")
    print("1. 在浏览器中查看和操作页面")
    print("2. 手动登录（如果需要）")
    print("3. 浏览题目和进行答题")
    print("4. 按 Ctrl+C 结束程序并关闭浏览器")
    print("=" * 60)
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n用户中断，准备关闭浏览器...")

def main():
    """
    主函数 - 循环爬取题目数据
    """
    print("=" * 60)
    print("🚀 考试宝题库自动爬取脚本")
    print("=" * 60)

    # 检查chromedriver.exe是否存在
    if not os.path.exists('chromedriver.exe'):
        print("❌ 未找到chromedriver.exe文件")
        print("请确保chromedriver.exe在当前目录中")
        return

    # 初始化浏览器
    driver = init_browser()
    if not driver:
        return

    try:
        # 打开考试宝网站
        print("📖 正在打开考试宝网站...")
        driver.get("https://www.kaoshibao.com")
        time.sleep(3)

        # 等待用户手动登录和导航到题目页面
        print("👤 请手动完成以下操作：")
        print("   1. 登录账号")
        print("   2. 选择题库")
        print("   3. 进入做题页面")
        print("   4. 完成后按回车键开始爬取...")
        input()

        # 尝试开启背题模式
        print("🔧 尝试开启背题模式...")
        enable_recite_mode(driver)

        # 询问用户要爬取多少题
        try:
            max_questions = int(input("📝 请输入要爬取的题目数量（输入0表示无限制）: "))
        except ValueError:
            max_questions = 10
            print(f"⚠️  输入无效，默认爬取 {max_questions} 题")

        print(f"🎯 开始爬取题目数据，目标数量: {'无限制' if max_questions == 0 else max_questions}")

        questions_data = []
        question_count = 0
        failed_count = 0
        max_failures = 5  # 最大连续失败次数

        # 创建图片保存目录
        images_dir = "scraped_questions/images"
        if not os.path.exists(images_dir):
            os.makedirs(images_dir)

        try:
            while True:
                # 检查是否达到目标数量
                if max_questions > 0 and question_count >= max_questions:
                    print(f"🎉 已完成目标数量 {max_questions} 题的爬取")
                    break

                print(f"\n📖 正在爬取第 {question_count + 1} 题...")

                # 提取当前题目数据
                question_data = extract_question_data(driver)

                if question_data and question_data['question']:
                    # 下载图片（如果有）
                    downloaded_images = []
                    if question_data.get('images'):
                        print(f"🖼️ 开始下载 {len(question_data['images'])} 张图片...")
                        for i, img_url in enumerate(question_data['images']):
                            filename = download_image(img_url, images_dir, question_count + 1, i + 1)
                            if filename:
                                downloaded_images.append(filename)

                    # 将下载的图片文件名保存到题目数据中
                    question_data['downloaded_images'] = downloaded_images

                    questions_data.append(question_data)
                    question_count += 1
                    failed_count = 0  # 重置失败计数

                    print(f"✅ 第 {question_count} 题爬取成功")
                    print(f"   题目: {question_data['question'][:50]}...")
                    print(f"   类型: {question_data['question_type']}")
                    print(f"   选项数: {len(question_data['options'])}")
                    print(f"   答案: {question_data['answer'][:20]}...")
                    if downloaded_images:
                        print(f"   图片: {len(downloaded_images)} 张")
                else:
                    failed_count += 1
                    print(f"❌ 第 {question_count + 1} 题爬取失败 (连续失败 {failed_count} 次)")

                    # 如果连续失败太多次，询问用户是否继续
                    if failed_count >= max_failures:
                        print(f"⚠️  连续失败 {max_failures} 次，可能页面结构发生变化")
                        continue_choice = input("是否继续尝试？(y/n): ").lower()
                        if continue_choice != 'y':
                            break
                        failed_count = 0  # 重置失败计数

                # 点击下一题
                print("🔄 正在切换到下一题...")
                if not click_next_question(driver):
                    print("⚠️  无法切换到下一题，可能已到最后一题")
                    # 询问用户是否手动切换
                    manual_choice = input("请手动切换到下一题，然后按回车继续（或输入 'q' 退出）: ")
                    if manual_choice.lower() == 'q':
                        break

                # 短暂延迟，避免请求过快
                time.sleep(1)

        except KeyboardInterrupt:
            print(f"\n⏹️  用户中断，已爬取 {question_count} 题")

        # 保存最终结果
        if questions_data:
            print(f"\n💾 正在保存最终结果...")
            final_file = save_questions_to_file(questions_data, "final_questions.txt")
            print(f"🎉 爬取完成！")
            print(f"   总共爬取: {len(questions_data)} 题")
            print(f"   保存位置: {final_file}")

            # 显示题目类型统计
            type_count = {}
            for q in questions_data:
                q_type = q.get('question_type', '未知')
                type_count[q_type] = type_count.get(q_type, 0) + 1

            print("\n📊 题目类型统计:")
            for q_type, count in type_count.items():
                print(f"   {q_type}: {count} 题")
        else:
            print("❌ 未爬取到任何题目数据")

    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 关闭浏览器
        if driver:
            print("\n🔒 正在关闭浏览器...")
            driver.quit()
            print("👋 程序已退出")

if __name__ == "__main__":
    main()
