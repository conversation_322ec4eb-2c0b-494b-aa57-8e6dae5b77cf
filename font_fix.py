#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考试宝字体反爬虫修复工具
用于修复被字体反爬虫机制影响的题目文本
"""

import re
import json

class FontAntiCrawlerFixer:
    def __init__(self):
        """初始化字符映射表"""
        # 根据你提供的数据分析出的字符映射关系
        self.char_mapping = {
            # 基础映射
            '拿': '组',
            '美': '组', 
            '但': '线',
            '伙': '机',
            '台': '台',  # 有些台是正确的
            '高台': '某台',
            '确': '路',
            '把': '标',
            '幺': '幺',
            '压': '压',
            '侧': '侧',
            '绕': '绕',
            '组': '组',
            '线': '线',
            '机': '机',
            
            # 更多映射关系
            '变压器': '变压器',
            '感应': '感应',
            '电动': '电动',
            '转子': '转子',
            '定子': '定子',
            '绕组': '绕组',
            '电阻': '电阻',
            '电压': '电压',
            '电流': '电流',
            '功率': '功率',
            '频率': '频率',
            '转速': '转速',
            '磁场': '磁场',
            '磁通': '磁通',
            '阻抗': '阻抗',
            '试验': '试验',
            '运行': '运行',
            '负载': '负载',
            '空载': '空载',
            '短路': '短路',
            '开路': '开路',
            '并联': '并联',
            '串联': '串联',
            '连接': '连接',
            '相位': '相位',
            '同步': '同步',
            '异步': '异步',
            '起动': '起动',
            '制动': '制动',
            '调速': '调速',
            '控制': '控制',
            '保护': '保护',
            '测量': '测量',
            '监测': '监测',
            '故障': '故障',
            '维护': '维护',
            '检修': '检修',
            '安装': '安装',
            '调试': '调试',
            '运维': '运维',
        }
        
        # 扩展映射表 - 根据观察到的错误字符
        self.extended_mapping = {
            # 从你的数据中观察到的错误映射
            '绕拿美': '绕组',
            '绕但式': '绕线式',
            '电动伙': '电动机',
            '高台': '某台',
            '短确': '短路',
            '把幺值': '标幺值',
            '一压侧': '一次侧',
            '二压侧': '二次侧',
            '低压侧': '低压侧',
            '高压侧': '高压侧',
            '实验': '试验',
            '确实验': '路试验',
            '短确实验': '短路试验',
            '空确实验': '空载试验',
            '负确实验': '负载试验',

            # 更多观察到的错误映射
            '铁吉': '铁芯',
            '高考座': '是由',
            '穿过铁芯但圈': '穿过铁芯线圈',
            '主磁牛': '主磁通',
            '负座时钟法': '负载时钟法',
            '连接拿时': '连接组时',
            '勒作': '看作',
            '短针': '短针',
            '历指': '所指',
            '连接拿领': '连接组号',
            '伙械': '机械',
            '采载': '负载',
            '稳定': '稳定',
            '了伙械损耗': '和机械损耗',
            '历对应': '所对应',
            '制动性质': '制动性质',
            '了术生遍': '和所产生',
            '相位': '相位',
            '短确阻抗': '短路阻抗',
            '假运行': '联运行',
            '采序阻抗': '负序阻抗',
            '相当赶': '相当于',
            '待损耗': '涡损耗',
            '带感性采载': '带感性负载',
            '曲但': '曲线',
            '是高可': '是一条',
            '状掉下': '状态下',
            '连接拿领': '连接组号',
            '球为': '因为',
            '空载环流': '空载环流',
            '高次绕拿': '高压绕组',
            '二次绕拿': '二次绕组',
            '磁曲对细': '磁极对数',
            '衣多': '越多',
            '衣': '越',
            '否在': '加在',
            '言用': '上用',
            '增否': '增大',
            '油率': '功率',
            '台同': '相同',
            '言': '上',
            '转迷': '转向',
            '鱼于': '处于',
            '拉旁': '某台',
            '老递': '传递',
            '方迷': '方向',
            '台同': '相同',
            '细开': '数字',
            '将一台绕组': '将一相绕组',
            '句一台绕组': '接一相绕组',
            '开母': '符号',
            '额宝': '额定',
            '拉旁': '在其',
            '心速': '调速',
            '采吸': '采用',
            '还损耗': '的损耗',
            '还物写量': '的物理量',
            '还作用': '的作用',
            '还三式底作状时': '的三种基本工作状态',
            '还是': '的是',
            '还损耗': '的损耗',
            '还空载损耗': '的空载损耗',
            '还空载实验': '的空载试验',
            '还': '的',
            
            # 更多常见错误
            '变压成': '变压器',
            '感应电': '感应电',
            '三相': '三相',
            '单相': '单相',
            '额定': '额定',
            '标准': '标准',
            '正常': '正常',
            '异常': '异常',
            '故障': '故障',
            '损耗': '损耗',
            '效率': '效率',
            '性能': '性能',
            '特性': '特性',
            '参数': '参数',
            '指标': '指标',
            '要求': '要求',
            '标准': '标准',
            '规范': '规范',
            '技术': '技术',
            '工艺': '工艺',
            '方法': '方法',
            '措施': '措施',
            '方案': '方案',
            '设计': '设计',
            '制造': '制造',
            '生产': '生产',
            '质量': '质量',
            '检验': '检验',
            '测试': '测试',
            '验收': '验收',
            '投运': '投运',
            '运行': '运行',
            '维护': '维护',
            '检修': '检修',
            '更换': '更换',
            '改造': '改造',
            '升级': '升级',
            '优化': '优化',
            '改进': '改进',
            '完善': '完善',
        }

    def fix_text(self, text):
        """修复文本中的字体反爬虫字符"""
        if not text:
            return text
            
        # 先进行扩展映射（多字符替换）
        for wrong, correct in self.extended_mapping.items():
            text = text.replace(wrong, correct)
        
        # 再进行单字符映射
        for wrong, correct in self.char_mapping.items():
            text = text.replace(wrong, correct)
        
        return text

    def fix_question_data(self, question_data):
        """修复题目数据"""
        if not question_data:
            return question_data
            
        # 修复题目内容
        if 'question' in question_data:
            question_data['question'] = self.fix_text(question_data['question'])
        
        # 修复选项
        if 'options' in question_data and question_data['options']:
            question_data['options'] = [self.fix_text(option) for option in question_data['options']]
        
        # 修复答案
        if 'answer' in question_data:
            question_data['answer'] = self.fix_text(question_data['answer'])
        
        # 修复解析
        if 'explanation' in question_data:
            question_data['explanation'] = self.fix_text(question_data['explanation'])
            
        return question_data

    def fix_questions_file(self, input_file, output_file):
        """修复题目文件"""
        try:
            # 读取原始数据
            with open(input_file, 'r', encoding='utf-8') as f:
                if input_file.endswith('.json'):
                    questions_data = json.load(f)
                else:
                    # 处理文本文件
                    content = f.read()
                    fixed_content = self.fix_text(content)
                    with open(output_file, 'w', encoding='utf-8') as out_f:
                        out_f.write(fixed_content)
                    print(f"✅ 文本文件修复完成: {output_file}")
                    return
            
            # 修复每个题目
            fixed_questions = []
            for question in questions_data:
                fixed_question = self.fix_question_data(question.copy())
                fixed_questions.append(fixed_question)
            
            # 保存修复后的数据
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(fixed_questions, f, ensure_ascii=False, indent=2)
            
            print(f"✅ JSON文件修复完成: {output_file}")
            print(f"   修复了 {len(fixed_questions)} 道题目")
            
        except Exception as e:
            print(f"❌ 修复文件时出错: {e}")

    def analyze_and_learn(self, text):
        """分析文本中可能的错误字符，用于扩展映射表"""
        # 这个方法可以用来分析新的错误字符模式
        suspicious_chars = []
        
        # 查找可能的错误字符（非常用汉字）
        for char in text:
            if '\u4e00' <= char <= '\u9fff':  # 汉字范围
                # 这里可以添加逻辑来识别可能的错误字符
                pass
        
        return suspicious_chars

def main():
    """主函数 - 修复已爬取的数据"""
    fixer = FontAntiCrawlerFixer()
    
    print("=" * 60)
    print("🔧 考试宝字体反爬虫修复工具")
    print("=" * 60)
    
    # 修复已有的数据文件
    import os
    scraped_dir = "scraped_questions"
    
    if os.path.exists(scraped_dir):
        for filename in os.listdir(scraped_dir):
            if filename.endswith('.txt') or filename.endswith('.json'):
                input_path = os.path.join(scraped_dir, filename)
                output_path = os.path.join(scraped_dir, f"fixed_{filename}")
                
                print(f"\n🔄 正在修复: {filename}")
                fixer.fix_questions_file(input_path, output_path)
    
    print("\n🎉 修复完成！")
    print("修复后的文件以 'fixed_' 开头")

if __name__ == "__main__":
    main()
