#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图片提取逻辑
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
import time

def _is_likely_content_image(img_element):
    """
    判断图片是否可能是内容图片（而不是UI图标）
    """
    try:
        # 获取图片尺寸
        width = img_element.get_attribute('width') or img_element.size.get('width', 0)
        height = img_element.get_attribute('height') or img_element.size.get('height', 0)

        # 转换为数字
        try:
            width = int(float(width)) if width else 0
            height = int(float(height)) if height else 0
        except:
            width = height = 0

        # 检查图片URL
        src = img_element.get_attribute('src') or ''
        
        # 如果是特定的UI图标，直接排除
        if 'icon-cut_topic.png' in src:
            return False
        
        # 如果是题目图片域名，优先考虑
        if 'up.zaixiankaoshi.com' in src:
            return True
            
        # 如果图片太小，可能是图标
        if width > 0 and height > 0:
            if width < 30 or height < 30:
                return False
            # 如果图片太小且是正方形，可能是图标
            if width < 80 and height < 80 and abs(width - height) < 10:
                return False

        # 检查alt属性
        alt = img_element.get_attribute('alt') or ''
        if any(keyword in alt.lower() for keyword in ['icon', 'logo', 'avatar', 'button', 'cut']):
            return False
            
        # 检查class属性
        class_name = img_element.get_attribute('class') or ''
        if any(keyword in class_name.lower() for keyword in ['icon', 'logo', 'ui', 'button']):
            return False

        return True
    except:
        return False

def test_image_extraction():
    """测试图片提取"""
    print("启动浏览器进行图片提取测试...")
    
    chrome_options = Options()
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    chrome_options.add_argument('--window-size=1920,1080')
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    try:
        service = Service('./chromedriver.exe')
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        print("请手动导航到题目页面，然后按回车开始测试...")
        input()
        
        print("\n=== 开始分析页面中的所有图片 ===")
        
        # 查找所有图片
        all_img_elements = driver.find_elements(By.TAG_NAME, "img")
        print(f"页面总共找到 {len(all_img_elements)} 张图片")
        
        valid_images = []
        ui_images = []
        
        for i, img in enumerate(all_img_elements):
            try:
                if img.is_displayed():
                    src = img.get_attribute('src') or ''
                    alt = img.get_attribute('alt') or ''
                    width = img.get_attribute('width') or img.size.get('width', 0)
                    height = img.get_attribute('height') or img.size.get('height', 0)
                    class_name = img.get_attribute('class') or ''
                    
                    print(f"\n图片 {i+1}:")
                    print(f"  URL: {src[:100]}...")
                    print(f"  尺寸: {width}x{height}")
                    print(f"  Alt: {alt}")
                    print(f"  Class: {class_name}")
                    
                    # 判断是否是内容图片
                    is_content = _is_likely_content_image(img)
                    print(f"  是否为内容图片: {is_content}")
                    
                    if is_content:
                        valid_images.append(src)
                        print("  ✅ 认定为题目图片")
                    else:
                        ui_images.append(src)
                        print("  ❌ 认定为UI图片")
                        
            except Exception as e:
                print(f"  错误: {e}")
        
        print(f"\n=== 分析结果 ===")
        print(f"题目图片数量: {len(valid_images)}")
        print(f"UI图片数量: {len(ui_images)}")
        
        if valid_images:
            print("\n题目图片列表:")
            for img in valid_images:
                print(f"  - {img}")
        else:
            print("\n⚠️  未找到题目图片")
            
        print("\nUI图片列表:")
        for img in ui_images[:5]:  # 只显示前5个
            print(f"  - {img}")
        if len(ui_images) > 5:
            print(f"  ... 还有 {len(ui_images) - 5} 个UI图片")
            
        input("\n按回车关闭浏览器...")
        
    except Exception as e:
        print(f"测试出错: {e}")
    finally:
        try:
            driver.quit()
        except:
            pass

if __name__ == "__main__":
    test_image_extraction()
